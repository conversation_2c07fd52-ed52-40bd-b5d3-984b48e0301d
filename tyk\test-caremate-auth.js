#!/usr/bin/env node

/**
 * Test script for CareMate Tyk Authentication and Authorization
 * Tests both public (keyless) and protected (JWT) endpoints
 */

const axios = require('axios');

// Configuration
const TYK_PUBLIC_URL = 'http://localhost:8181/caremate/api';
const TYK_PROTECTED_URL = 'http://localhost:8181/caremate/protected';

// Test credentials from user requirements
const testCredentials = [
  {
    name: "Admin User",
    email: "<EMAIL>",
    password: "Pa$$w0rd!"
  },
  {
    name: "Kiosk User", 
    email: "<EMAIL>",
    password: "Pa$$w0rd!"
  }
];

/**
 * Make HTTP request
 */
async function makeRequest(method, url, headers = {}, data = null) {
  try {
    const config = {
      method: method.toLowerCase(),
      url: url,
      headers: headers,
      validateStatus: () => true // Don't throw on HTTP error status
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return {
      status: response.status,
      data: response.data,
      headers: response.headers
    };
  } catch (error) {
    return {
      status: 0,
      error: error.message,
      data: null
    };
  }
}

/**
 * Test login and get JWT token
 */
async function testLogin(credentials) {
  console.log(`\n--- Testing login for ${credentials.name} ---`);
  
  const response = await makeRequest('POST', `${TYK_PUBLIC_URL}/auth/login`, {
    'Content-Type': 'application/json'
  }, {
    email: credentials.email,
    password: credentials.password
  });

  if (response.status === 200 && response.data.status) {
    console.log('✅ Login successful');
    const token = response.data.data.tokens.access.token;
    const permissions = response.data.data.permissions || [];
    console.log(`   Token received (${token.substring(0, 20)}...)`);
    console.log(`   Permissions: ${permissions.length} found`);
    return token;
  } else {
    console.log('❌ Login failed:', response.status, response.data);
    return null;
  }
}

/**
 * Test public endpoints (keyless)
 */
async function testPublicEndpoints() {
  console.log('\n=== Testing Public (Keyless) Endpoints ===');
  
  const endpoints = [
    { method: 'GET', path: '/health', description: 'Health check' },
    { method: 'GET', path: '/countries', description: 'Countries list' },
    { method: 'GET', path: '/states', description: 'States list' },
    { method: 'GET', path: '/timezones', description: 'Timezones list' }
  ];

  for (const endpoint of endpoints) {
    const response = await makeRequest(endpoint.method, `${TYK_PUBLIC_URL}${endpoint.path}`);
    console.log(`${endpoint.method} ${endpoint.path} (${endpoint.description}): Status ${response.status}`);
    
    if (response.status < 400) {
      console.log('  ✅ Public endpoint accessible');
    } else {
      console.log('  ❌ Public endpoint should be accessible');
      if (response.data) {
        console.log(`     Error: ${JSON.stringify(response.data).substring(0, 100)}...`);
      }
    }
  }
}

/**
 * Test protected endpoints with JWT
 */
async function testProtectedEndpoints(token, userType) {
  console.log(`\n=== Testing Protected Endpoints (${userType}) ===`);
  
  const endpoints = [
    { method: 'GET', path: '/facility', description: 'View facilities', requiredPermissions: ['view_facilities'] },
    { method: 'GET', path: '/identity', description: 'View identities', requiredPermissions: ['view_identity'] },
    { method: 'GET', path: '/system', description: 'View systems', requiredPermissions: ['view_systems'] },
    { method: 'GET', path: '/guests', description: 'View guests', requiredPermissions: ['view_guests'] },
    { method: 'GET', path: '/countries', description: 'Countries (no auth required)', requiredPermissions: [] }
  ];

  for (const endpoint of endpoints) {
    const response = await makeRequest(
      endpoint.method, 
      `${TYK_PROTECTED_URL}${endpoint.path}`,
      {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    );

    console.log(`${endpoint.method} ${endpoint.path} (${endpoint.description}): Status ${response.status}`);
    
    if (response.status === 200) {
      console.log('  ✅ Access granted');
    } else if (response.status === 401) {
      console.log('  ❌ Authentication failed (401)');
    } else if (response.status === 403) {
      console.log('  ⚠️  Access denied - insufficient permissions (403)');
    } else {
      console.log(`  ⚠️  Unexpected status: ${response.status}`);
      if (response.data) {
        console.log(`     Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
      }
    }
  }
}

/**
 * Test protected endpoints without token
 */
async function testProtectedWithoutToken() {
  console.log('\n=== Testing Protected Endpoints Without Token ===');
  
  const response = await makeRequest('GET', `${TYK_PROTECTED_URL}/facility`);
  console.log(`GET /facility (no token): Status ${response.status}`);
  
  if (response.status === 401) {
    console.log('  ✅ Correctly rejected (401 Unauthorized)');
  } else {
    console.log('  ❌ Should have been rejected with 401');
    if (response.data) {
      console.log(`     Response: ${JSON.stringify(response.data)}`);
    }
  }
}

/**
 * Test protected endpoints with invalid token
 */
async function testProtectedWithInvalidToken() {
  console.log('\n=== Testing Protected Endpoints With Invalid Token ===');
  
  const response = await makeRequest('GET', `${TYK_PROTECTED_URL}/facility`, {
    'Authorization': 'Bearer invalid.jwt.token'
  });
  
  console.log(`GET /facility (invalid token): Status ${response.status}`);
  
  if (response.status === 401) {
    console.log('  ✅ Correctly rejected (401 Unauthorized)');
  } else {
    console.log('  ❌ Should have been rejected with 401');
    if (response.data) {
      console.log(`     Response: ${JSON.stringify(response.data)}`);
    }
  }
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Starting CareMate Tyk Authentication Tests');
  console.log(`Public URL: ${TYK_PUBLIC_URL}`);
  console.log(`Protected URL: ${TYK_PROTECTED_URL}`);
  
  try {
    // Test public endpoints
    await testPublicEndpoints();
    
    // Test protected endpoints without token
    await testProtectedWithoutToken();
    
    // Test protected endpoints with invalid token
    await testProtectedWithInvalidToken();
    
    // Test login and protected endpoints for each user
    for (const credentials of testCredentials) {
      const token = await testLogin(credentials);
      if (token) {
        await testProtectedEndpoints(token, credentials.name);
      }
    }
    
    console.log('\n✅ All tests completed!');
    console.log('\nSetup Summary:');
    console.log('- Public API: /caremate/api/ (keyless, handles auth endpoints)');
    console.log('- Protected API: /caremate/protected/ (JWT required, permission checks)');
    console.log('- Middleware: Validates permissions and sets headers for downstream API');
    
  } catch (error) {
    console.error('\n❌ Test execution failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  makeRequest,
  testLogin,
  testCredentials
};
