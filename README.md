# Project Setup

This project uses shared code for **models**, **config**, **utils**, and **services** across multiple sub-projects (API, processor, crons, etc.). 
To keep a single source of truth and avoid code duplication, we use symbolic links.

## Setup Instructions

1. **Prerequisites:**

   - **Node.js**: Ensure Node.js is installed.
   - **Developer Mode / Administrative Rights (Windows):** Creating symlinks on Windows might require:
   - Running commands in an elevated Command Prompt (as Administrator), or
   - Enabling Developer Mode (Windows 10/11)

2. **Install Dependencies:**

   Run the following command from the project root to install required packages:

   ```bash
   npm install
   ```

3. **Create Symlinks:**

    A custom script has been set up to automatically create symlinks for the shared directories. Run the following command from the project root:
    
    ```bash
    npm run setup
    ```   

3. **Remove Symlinks:**
    
    To remove all symlinks (shared directories and `.env` files), run:
     
    ```bash
    npm run setup:unlink
   ```


## Notes

1. **Environment Files:**

- All .env.\* files from the project root will be symlinked to each subproject.
- If a regular file exists at the destination, it will be deleted before creating the symlink.
- Existing Symlinks(The setup script checks for existing symlinks and either) :
   - Skips them if they point to the correct location
   - Replaces them if they point to a different location