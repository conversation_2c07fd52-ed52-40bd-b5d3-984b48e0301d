{"name": "CareMate JWT API", "api_id": "caremate-jwt", "org_id": "default", "definition": {"location": "header", "key": "version"}, "auth": {"auth_header_name": "Authorization"}, "enable_jwt": false, "use_keyless": true, "jwt_signing_method": "hmac", "jwt_source": "header:Authorization", "jwt_identity_base_field": "sub", "jwt_client_base_field": "", "jwt_policy_field_name": "", "jwt_default_policies": [], "jwt_issued_at_validation_skew": 0, "jwt_expires_at_validation_skew": 0, "jwt_not_before_validation_skew": 0, "jwt_skip_kid": false, "jwt_scope_to_policy_mapping": {}, "jwt_scope_claim_name": "", "jwt_secret": "THIS_IS_MY_NEW_KEY_FOR_FUTURE", "custom_middleware": {"driver": "otto", "post": [{"name": "CareMateAuth", "path": "./middleware/caremate-auth.js", "require_session": false, "raw_body_only": false}]}, "version_data": {"not_versioned": true, "versions": {"Default": {"name": "<PERSON><PERSON><PERSON>"}}}, "proxy": {"listen_path": "/caremate/protected/", "target_url": "http://************:3001/api/", "strip_listen_path": true}}